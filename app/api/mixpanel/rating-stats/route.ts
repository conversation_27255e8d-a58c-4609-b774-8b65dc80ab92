import { 
  NextRequest, NextResponse,
} from "next/server";

interface RatingStats {
  buy: number;
  hold: number;
  sell: number;
  total: number;
}

interface MixpanelEvent {
  properties: {
    rating?: string;
    slug?: string;
    previousRating?: string | null;
    isUpdate?: boolean;
    timestamp?: string;
    [key: string]: any;
  };
}

export async function POST (request: NextRequest) {
  try {
    const { 
      slug, from_date, to_date,
    } = await request.json();

    const MIXPANEL_API_SECRET = process.env.MIXPANEL_API_SECRET;

    const auth = Buffer.from(`${MIXPANEL_API_SECRET}:`).toString("base64");

    let fromDate = from_date;
    const toDate = to_date || new Date().toISOString().split("T")[0];

    if (!fromDate) {
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
      fromDate = ninetyDaysAgo.toISOString().split("T")[0];
    }

    const exportUrl = `https://data-eu.mixpanel.com/api/2.0/export`;
    const exportParams = new URLSearchParams({
      from_date: fromDate,
      to_date: toDate,
      event: '["Stock Rating Submitted"]',
      where: `properties["slug"] == "${slug}"`,
    });

    const response = await fetch(`${exportUrl}?${exportParams}`, {
      method: "GET",
      headers: {
        Authorization: `Basic ${auth}`,
        Accept: "text/plain",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Mixpanel API error:", response.status, errorText);
      console.error("Request URL:", `${exportUrl}?${exportParams}`);
      console.error("Request headers:", {
        Authorization: `Basic ${auth}`,
        Accept: "text/plain",
      });
      return NextResponse.json(
        { error: `Mixpanel API error: ${response.status} - ${errorText}` },
        { status: 500 },
      );
    }

    let responseText = "";
    try {
      if (!response.body) {
        throw new Error("No response body");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const chunks: string[] = [];
      let totalSize = 0;
      const maxSize = 50 * 1024 * 1024;

      while (true) {
        const { 
          done, value,
        } = await reader.read();

        if (done) {
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        totalSize += chunk.length;

        if (totalSize > maxSize) {
          break;
        }

        chunks.push(chunk);
      }

      chunks.push(decoder.decode());
      responseText = chunks.join("");
    } catch (textError) {
      console.error("Error reading response text:", textError);
      return NextResponse.json(
        { error: "Failed to read response from Mixpanel API" },
        { status: 500 },
      );
    }

    const events: MixpanelEvent[] = [];

    try {
      if (responseText.trim()) {
        const lines = responseText.trim().split("\n");
        for (const line of lines) {
          try {
            if (line.trim()) {
              const event = JSON.parse(line);
              if (
                event.event === "Stock Rating Submitted" &&
                event.properties
              ) {
                events.push({
                  properties: event.properties,
                });
              }
            }
          } catch (parseError) {
            console.warn("Failed to parse line:", line, parseError);
          }
        }
      }
    } catch (parseError) {
      console.error("Error parsing response:", parseError);
    }

    const stats: RatingStats = {
      buy: 0,
      hold: 0,
      sell: 0,
      total: 0,
    };

    events.forEach((event) => {
      const rating = event.properties?.rating?.toLowerCase();
      const previousRating = event.properties?.previousRating?.toLowerCase();
      const isUpdate = event.properties?.isUpdate;

      if (isUpdate && previousRating) {
        if (previousRating === "buy") {
          stats.buy = Math.max(0, stats.buy - 1);
        } else if (previousRating === "hold") {
          stats.hold = Math.max(0, stats.hold - 1);
        } else if (previousRating === "sell") {
          stats.sell = Math.max(0, stats.sell - 1);
        }
      }

      if (rating === "buy") {
        stats.buy++;
      } else if (rating === "hold") {
        stats.hold++;
      } else if (rating === "sell") {
        stats.sell++;
      }
    });

    stats.total = stats.buy + stats.hold + stats.sell;

    if (stats.total === 0) {
      const sampleStats = {
        buy: 0,
        hold: 0,
        sell: 0,
        total: 0,
      };
      sampleStats.total = sampleStats.buy + sampleStats.hold + sampleStats.sell;

      return NextResponse.json(
        {
          stats: sampleStats,
          note: "Sample data - no actual ratings found in Mixpanel",
        },
        {
          headers: {
            "Cache-Control":
              "no-store, no-cache, must-revalidate, proxy-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        },
      );
    }

    return NextResponse.json(
      { stats },
      {
        headers: {
          "Cache-Control":
            "no-store, no-cache, must-revalidate, proxy-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      },
    );
  } catch (error) {
    console.error("Error fetching rating stats:", error);
    return NextResponse.json(
      {
        error: `Failed to fetch rating stats: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 },
    );
  }
}
